import 'dart:convert';
import 'package:dandanplay_flutter/model/danmaku.dart';
import 'package:dandanplay_flutter/model/subtitle_style.dart';
import 'package:get_it/get_it.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:signals_flutter/signals_flutter.dart';

/// 配置服务
/// 提供类型安全的配置项管理，支持默认值和持久化存储
class ConfigureService {
  final SharedPreferences _prefs;
  late final Signal<double> defaultPlaySpeed = Signal(
    _prefs.getDouble('defaultPlaySpeed') ?? 1.0,
  );
  late final Signal<bool> hardwareDecoderEnable = Signal(
    _prefs.getBool('hardwareDecoderEnable') ?? true,
  );
  late final Signal<String> hardwareDecoder = Signal(
    _prefs.getString('hardwareDecoder') ?? 'auto',
  );
  late final Signal<bool> lowMemoryMode = Signal(
    _prefs.getBool('lowMemoryMode') ?? false,
  );
  // TODO
  late final Signal<bool> playerDebugMode = Signal(
    _prefs.getBool('playerDebugMode') ?? false,
  );
  late final Signal<DanmakuSettings> danmakuSettings = Signal(
    getDanmakuSettings(),
  );
  late final Signal<SubtitleStyle> subtitleStyle = Signal(getSubtitleStyle());
  late final Signal<String> themeMode = Signal(
    _prefs.getString('themeMode') ?? '0',
  );

  late final Signal<String> themeColor = Signal(
    _prefs.getString('themeColor') ?? 'blue',
  );
  // TODO
  late final Signal<bool> syncEnable = Signal(
    _prefs.getBool('syncEnable') ?? false,
  );

  /// 日志级别配置 (0: DEBUG, 1: INFO, 2: WARNING, 3: ERROR)
  late final Signal<String> logLevel = Signal(
    _prefs.getString('logLevel') ?? '1', // 默认为INFO级别
  );
  // TODO
  late final Signal<String> webDavURL = Signal(
    _prefs.getString('webDavURL') ?? '',
  );
  // TODO
  late final Signal<String> webDavUsername = Signal(
    _prefs.getString('webDavUsername') ?? '',
  );
  // TODO
  late final Signal<String> webDavPassword = Signal(
    _prefs.getString('webDavPassword') ?? '',
  );
  ConfigureService(this._prefs);

  /// 注册配置服务到依赖注入容器
  static Future<ConfigureService> register(SharedPreferences p) async {
    var service = ConfigureService(p);
    service.setupSignalListeners();
    GetIt.I.registerSingleton<ConfigureService>(service);
    return service;
  }

  void setupSignalListeners() {
    effect(() {
      _prefs.setDouble('defaultPlaySpeed', defaultPlaySpeed.value);
    });
    effect(() {
      _prefs.setBool('hardwareDecoderEnable', hardwareDecoderEnable.value);
    });
    effect(() {
      _prefs.setString('hardwareDecoder', hardwareDecoder.value);
    });
    effect(() {
      _prefs.setBool('lowMemoryMode', lowMemoryMode.value);
    });
    effect(() {
      _prefs.setBool('playerDebugMode', playerDebugMode.value);
    });
    effect(() {
      setDanmakuSettings(danmakuSettings.value);
    });
    effect(() {
      setSubtitleStyle(subtitleStyle.value);
    });
    effect(() {
      _prefs.setString('themeMode', themeMode.value);
    });
    effect(() {
      _prefs.setString('themeColor', themeColor.value);
    });
    effect(() {
      _prefs.setBool('syncEnable', syncEnable.value);
    });
    effect(() {
      _prefs.setString('webDavURL', webDavURL.value);
    });
    effect(() {
      _prefs.setString('webDavUsername', webDavUsername.value);
    });
    effect(() {
      _prefs.setString('webDavPassword', webDavPassword.value);
    });
    effect(() {
      _prefs.setString('logLevel', logLevel.value);
    });
  }

  DanmakuSettings getDanmakuSettings() {
    final jsonString = _prefs.getString('danmakuSettings');
    if (jsonString == null) {
      return DanmakuSettings();
    }
    return DanmakuSettings.fromJson(
      jsonDecode(utf8.decode(base64Decode(jsonString))),
    );
  }

  Future<void> setDanmakuSettings(DanmakuSettings settings) async {
    await _prefs.setString(
      'danmakuSettings',
      base64Encode(utf8.encode(jsonEncode(settings.toJson()))),
    );
  }

  SubtitleStyle getSubtitleStyle() {
    final jsonString = _prefs.getString('subtitleStyle');
    if (jsonString == null) {
      return SubtitleStyle();
    }
    return SubtitleStyle.fromJson(
      jsonDecode(utf8.decode(base64Decode(jsonString))),
    );
  }

  Future<void> setSubtitleStyle(SubtitleStyle style) async {
    await _prefs.setString(
      'subtitleStyle',
      base64Encode(utf8.encode(jsonEncode(style.toJson()))),
    );
  }
}
