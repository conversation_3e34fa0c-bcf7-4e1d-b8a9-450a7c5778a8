import 'package:dandanplay_flutter/model/file_item.dart';
import 'package:dandanplay_flutter/router.dart';
import 'package:dandanplay_flutter/service/file_explorer.dart';
import 'package:dandanplay_flutter/service/media_library.dart';
import 'package:dandanplay_flutter/service/storage.dart';
import 'package:dandanplay_flutter/widget/video_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:signals_flutter/signals_flutter.dart';

class FileExplorerPage extends StatefulWidget {
  final int mediaLibraryId;
  final String mediaLibraryName;
  const FileExplorerPage({
    super.key,
    required this.mediaLibraryId,
    required this.mediaLibraryName,
  });

  @override
  State<FileExplorerPage> createState() => _FileExplorerPageState();
}

class _FileExplorerPageState extends State<FileExplorerPage> {
  MediaLibrary? _mediaLibrary;
  final FileExplorerService _fileExplorerService =
      GetIt.I.get<FileExplorerService>();
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    init();
    super.initState();
  }

  void init() async {
    final mediaLibraryService = GetIt.I.get<MediaLibraryService>();
    final mediaLibrary = await mediaLibraryService.getMediaLibrary(
      widget.mediaLibraryId,
    );
    if (mediaLibrary == null) {
      return;
    }
    late FileExplorerProvider provider;
    switch (mediaLibrary.mediaLibraryType) {
      case MediaLibraryType.webdav:
        provider = WebDAVFileExplorerProvider(mediaLibrary);
        break;
      case MediaLibraryType.ftp:
        break;
      case MediaLibraryType.smb:
        break;
      case MediaLibraryType.local:
        provider = LocalFileExplorerProvider();
        break;
    }
    _fileExplorerService.setProvider(provider, mediaLibrary);
    setState(() {
      _mediaLibrary = mediaLibrary;
    });
  }

  void _playVideo(String path, int index) {
    _fileExplorerService.changeVideo(index, path);
    final location = Uri(path: videoPlayerPath);
    context.push(location.toString()).then((_) => _refresh());
  }

  Future<void> _refresh() async {
    await _fileExplorerService.refresh();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          if (!_fileExplorerService.goBack()) {
            Navigator.of(context).pop();
          }
        }
      },
      child: Scaffold(
        appBar: AppBar(
          systemOverlayStyle: const SystemUiOverlayStyle(
            systemNavigationBarColor: Colors.transparent,
          ),
          scrolledUnderElevation: 0,
          title: Row(
            children: [
              Text(
                widget.mediaLibraryName,
                style: context.theme.typography.xl2.copyWith(height: 1.2),
              ),
            ],
          ),
        ),
        body:
            _mediaLibrary == null
                ? const Center(child: CircularProgressIndicator())
                : RefreshIndicator(
                  onRefresh: _refresh,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        controller: _scrollController,
                        child: Watch((context) {
                          final path = _fileExplorerService.path.watch(context);
                          final parts =
                              path
                                  .split('/')
                                  .where((p) => p.isNotEmpty)
                                  .toList();

                          WidgetsBinding.instance.addPostFrameCallback((_) {
                            if (_scrollController.hasClients &&
                                _scrollController.position.maxScrollExtent >
                                    0) {
                              _scrollController.jumpTo(
                                _scrollController.position.maxScrollExtent,
                              );
                            }
                          });

                          final children = <Widget>[
                            FBreadcrumbItem(
                              onPress: () => _fileExplorerService.cd('/'),
                              child: Text(
                                '根目录',
                                style: TextStyle(
                                  color:
                                      parts.isEmpty
                                          ? context.theme.colors.primary
                                          : context.theme.colors.foreground,
                                ),
                              ),
                            ),
                          ];

                          var currentPath = '';
                          for (var i = 0; i < parts.length; i++) {
                            final part = parts[i];
                            currentPath += '/$part';
                            final targetPath = currentPath;
                            final isLast = i == parts.length - 1;
                            children.add(
                              FBreadcrumbItem(
                                onPress:
                                    isLast
                                        ? null
                                        : () =>
                                            _fileExplorerService.cd(targetPath),
                                child: Text(
                                  part,
                                  style: TextStyle(
                                    color:
                                        isLast
                                            ? context.theme.colors.primary
                                            : context.theme.colors.foreground,
                                  ),
                                ),
                              ),
                            );
                          }

                          return Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: 8,
                            ),
                            child: FBreadcrumb(children: children),
                          );
                        }),
                      ),
                      Expanded(
                        child: Watch(
                          (context) => _fileExplorerService.files.value.map(
                            data: (files) {
                              if (files.isEmpty) {
                                return Center(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Icon(
                                        FIcons.folder,
                                        size: 48,
                                        color:
                                            context
                                                .theme
                                                .colors
                                                .mutedForeground,
                                      ),
                                      const SizedBox(height: 16),
                                      Text(
                                        '此文件夹为空',
                                        style: context.theme.typography.xl,
                                      ),
                                    ],
                                  ),
                                );
                              }
                              return CustomScrollView(
                                slivers: [
                                  SliverToBoxAdapter(
                                    child: FItemGroup(
                                      divider: FItemDivider.indented,
                                      children: _listBuilder(files),
                                    ),
                                  ),
                                ],
                              );
                            },
                            error:
                                (error, stack) =>
                                    const Center(child: Text('加载失败')),
                            loading:
                                () => const Center(
                                  child: CircularProgressIndicator(),
                                ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
      ),
    );
  }

  List<FItemMixin> _listBuilder(List<FileItem> files) {
    final widgetList = <FItemMixin>[];
    for (var i = 0; i < files.length; i++) {
      final file = files[i];
      if (file.isFolder) {
        widgetList.add(
          FItem(
            prefix: const Icon(FIcons.folder, size: 40),
            title: Text(
              file.name,
              style: context.theme.typography.base,
              maxLines: 2,
            ),
            subtitle: Text('目录'),
            onPress: () => {_fileExplorerService.goDir(file.name)},
          ),
        );
        continue;
      }
      widgetList.add(
        VideoItem(
          history: file.history,
          name: file.name,
          onPress: () => _playVideo(file.path, i),
        ),
      );
    }
    return widgetList;
  }
}
