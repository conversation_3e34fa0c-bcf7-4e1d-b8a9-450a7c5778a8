import 'package:dandanplay_flutter/service/configure.dart';
import 'package:dandanplay_flutter/widget/settings/radio_settings_section.dart';
import 'package:dandanplay_flutter/widget/settings/settings_section.dart';
import 'package:dandanplay_flutter/widget/settings/settings_tile.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:go_router/go_router.dart';
import 'package:signals_flutter/signals_flutter.dart';

class PlayerSettingsPage extends StatelessWidget {
  const PlayerSettingsPage({super.key});

  @override
  Widget build(BuildContext context) {
    final configure = GetIt.I<ConfigureService>();
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          systemNavigationBarColor: Colors.transparent,
        ),
        scrolledUnderElevation: 0,
        title: const Text('播放器设置'),
      ),
      body: Padding(
        padding: context.theme.scaffoldStyle.childPadding,
        child: ListView(
          children: [
            SettingsSection(
              title: '播放配置',
              children: [
                Watch((context) {
                  final speed = configure.defaultPlaySpeed.value;
                  return SettingsTile.sliderTile(
                    title: '默认播放速度',
                    silderValue: speed,
                    silderMin: 0.25,
                    silderMax: 4,
                    onSilderChange: (value) {
                      configure.defaultPlaySpeed.value = value;
                    },
                    details: '${speed.toStringAsFixed(2)}x',
                    silderDivisions: 15,
                  );
                }),
              ],
            ),
            SettingsSection(
              title: '解码',
              children: [
                Watch((context) {
                  return SettingsTile.switchTile(
                    title: '启用硬解',
                    switchValue: configure.hardwareDecoderEnable.value,
                    onBoolChange: (value) {
                      configure.hardwareDecoderEnable.value = value;
                    },
                  );
                }),
                Watch((context) {
                  return SettingsTile.navigationTile(
                    title: '硬件解码器',
                    subtitle: configure.hardwareDecoder.value,
                    onPress: () {
                      context.push('/settings/player/hardware-decoder');
                    },
                  );
                }),
              ],
            ),
            SettingsSection(
              title: '其他',
              children: [
                Watch((context) {
                  return SettingsTile.switchTile(
                    title: '低内存模式',
                    switchValue: configure.lowMemoryMode.value,
                    onBoolChange: (value) {
                      configure.lowMemoryMode.value = value;
                    },
                  );
                }),
                Watch((context) {
                  return SettingsTile.switchTile(
                    title: '播放器调试模式',
                    switchValue: configure.playerDebugMode.value,
                    onBoolChange: (value) {
                      configure.playerDebugMode.value = value;
                    },
                  );
                }),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class HardwareDecoderPage extends StatelessWidget {
  const HardwareDecoderPage({super.key});

  // 可选硬件解码器
  static const Map<String, String> hardwareDecodersList = {
    'auto': '启用任意可用解码器',
    'auto-safe': '启用最佳解码器',
    'auto-copy': '启用带拷贝功能的最佳解码器',
    'd3d11va': 'DirectX11 (windows8 及以上)',
    'd3d11va-copy': 'DirectX11 (windows8 及以上) (非直通)',
    'videotoolbox': 'VideoToolbox (macOS / iOS)',
    'videotoolbox-copy': 'VideoToolbox (macOS / iOS) (非直通)',
    'vaapi': 'VAAPI (Linux)',
    'vaapi-copy': 'VAAPI (Linux) (非直通)',
    'nvdec': 'NVDEC (NVIDIA独占)',
    'nvdec-copy': 'NVDEC (NVIDIA独占) (非直通)',
    'drm': 'DRM (Linux)',
    'drm-copy': 'DRM (Linux) (非直通)',
    'vulkan': 'Vulkan (全平台) (实验性)',
    'vulkan-copy': 'Vulkan (全平台) (实验性) (非直通)',
    'dxva2': 'DXVA2 (Windows7 及以上)',
    'dxva2-copy': 'DXVA2 (Windows7 及以上) (非直通)',
    'vdpau': 'VDPAU (Linux)',
    'vdpau-copy': 'VDPAU (Linux) (非直通)',
    'mediacodec': 'MediaCodec (Android)',
    'mediacodec-copy': 'MediaCodec (Android) (非直通)',
    'cuda': 'CUDA (NVIDIA独占) (过时)',
    'cuda-copy': 'CUDA (NVIDIA独占) (过时) (非直通)',
    'crystalhd': 'CrystalHD (全平台) (过时)',
    'rkmpp': 'Rockchip MPP (仅部分Rockchip芯片)',
  };

  @override
  Widget build(BuildContext context) {
    final configure = GetIt.I<ConfigureService>();
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          systemNavigationBarColor: Colors.transparent,
        ),
        scrolledUnderElevation: 0,
        title: const Text('硬件解码器'),
      ),
      body: Padding(
        padding: context.theme.scaffoldStyle.childPadding,
        child: SingleChildScrollView(
          child: Watch((context) {
            return RadioSettingsSection(
              options: hardwareDecodersList,
              value: configure.hardwareDecoder.value,
              onChange: (value) {
                configure.hardwareDecoder.value = value;
              },
            );
          }),
        ),
      ),
    );
  }
}
