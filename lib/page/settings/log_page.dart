import 'dart:io';
import 'package:dandanplay_flutter/widget/settings/settings_section.dart';
import 'package:dandanplay_flutter/widget/settings/settings_tile.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:forui/forui.dart';
import 'package:get_it/get_it.dart';
import 'package:dandanplay_flutter/service/logger.dart';
import 'package:dandanplay_flutter/service/configure.dart';
import 'package:file_picker/file_picker.dart';
import 'package:signals_flutter/signals_flutter.dart';

class LogPage extends StatefulWidget {
  const LogPage({super.key});

  @override
  State<LogPage> createState() => _LogPageState();
}

class _LogPageState extends State<LogPage> {
  late LoggerService _loggerService;
  late ConfigureService _configureService;

  @override
  void initState() {
    super.initState();
    _loggerService = GetIt.I.get<LoggerService>();
    _configureService = GetIt.I.get<ConfigureService>();
  }

  // 导出日志文件
  Future<void> _exportLogFile(File file) async {
    try {
      final fileName = file.uri.pathSegments.last;
      // 让用户选择导出位置
      await FilePicker.platform.saveFile(
        fileName: fileName,
        bytes: file.readAsBytesSync(),
      );
      if (mounted) {
        showFToast(context: context, title: const Text('导出日志成功'));
      }
    } catch (e) {
      if (mounted) {
        showFToast(context: context, title: Text('导出日志失败: $e'));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        systemOverlayStyle: const SystemUiOverlayStyle(
          systemNavigationBarColor: Colors.transparent,
        ),
        title: const Text('日志设置'),
        scrolledUnderElevation: 0,
      ),
      body: Padding(
        padding: context.theme.scaffoldStyle.childPadding,
        child: ListView(
          children: [
            SettingsSection(
              title: '日志级别',
              children: [
                Watch((context) {
                  return SettingsTile.radioTile(
                    title: '日志级别',
                    radioValue: _configureService.logLevel.value,
                    onRadioChange: (value) {
                      _configureService.logLevel.value = value;
                    },
                    radioOptions: {
                      'DEBUG': '0',
                      'INFO': '1',
                      'WARNING': '2',
                      'ERROR': '3',
                    },
                  );
                }),
              ],
            ),
            FutureBuilder<List<File>>(
              future: _loggerService.getLogFiles(),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const SizedBox();
                }
                if (snapshot.hasError) {
                  return const SizedBox();
                }
                final logFiles = snapshot.data ?? [];
                if (logFiles.isEmpty) {
                  return SettingsSection(
                    title: '日志文件',
                    children: [SettingsTile.simpleTile(title: '暂无日志文件')],
                  );
                }
                // 按修改时间排序，最新的在前面
                logFiles.sort(
                  (a, b) =>
                      b.lastModifiedSync().compareTo(a.lastModifiedSync()),
                );
                return SettingsSection(
                  title: '日志文件',
                  children:
                      logFiles.map((file) {
                        final fileName = file.uri.pathSegments.last;
                        return SettingsTile.simpleTile(
                          title: fileName,
                          onPress: () => _exportLogFile(file),
                        );
                      }).toList(),
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
