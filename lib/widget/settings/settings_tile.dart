import 'package:dandanplay_flutter/theme/select_menu_tile_style.dart';
import 'package:dandanplay_flutter/theme/tile_style.dart';
import 'package:forui/forui.dart';
import 'package:flutter/material.dart';

enum SettingsTileType {
  simpleTile,
  switchTile,
  sliderTile,
  navigationTile,
  radioTile,
}

class SettingsTile extends StatelessWidget with FTileMixin {
  SettingsTile.simpleTile({
    super.key,
    required this.title,
    this.subtitle,
    this.details,
    this.suffix,
    this.onPress,
  }) {
    onBoolChange = null;
    onSilderChange = null;
    onRadioChange = null;
    radioOptions = null;
    switchValue = null;
    silderValue = null;
    silderDivisions = null;
    silderMin = null;
    silderMax = null;
    radioValue = null;
    type = SettingsTileType.simpleTile;
  }
  SettingsTile.switchTile({
    super.key,
    required this.title,
    this.subtitle,
    this.details,
    this.suffix,
    required this.onBoolChange,
    required this.switchValue,
  }) {
    onPress = () => onBoolChange!(!switchValue!);
    onSilderChange = null;
    onRadioChange = null;
    radioOptions = null;
    silderValue = null;
    silderDivisions = null;
    silderMin = null;
    silderMax = null;
    radioValue = null;
    type = SettingsTileType.switchTile;
  }
  SettingsTile.sliderTile({
    super.key,
    required this.title,
    this.details,
    this.subtitle,
    this.suffix,
    required this.onSilderChange,
    required this.silderValue,
    required this.silderDivisions,
    required this.silderMin,
    required this.silderMax,
  }) {
    onPress = null;
    onBoolChange = null;
    onRadioChange = null;
    radioOptions = null;
    switchValue = null;
    radioValue = null;
    type = SettingsTileType.sliderTile;
  }
  SettingsTile.navigationTile({
    super.key,
    required this.title,
    this.subtitle,
    this.details,
    this.onPress,
  }) {
    suffix = const Icon(FIcons.chevronRight);
    onBoolChange = null;
    onSilderChange = null;
    onRadioChange = null;
    radioOptions = null;
    switchValue = null;
    silderValue = null;
    silderDivisions = null;
    silderMin = null;
    silderMax = null;
    radioValue = null;
    type = SettingsTileType.navigationTile;
  }
  SettingsTile.radioTile({
    super.key,
    required this.title,
    this.subtitle,
    this.suffix,
    required this.onRadioChange,
    required this.radioOptions,
    required this.radioValue,
  }) {
    details = null;
    onPress = null;
    onBoolChange = null;
    onSilderChange = null;
    switchValue = null;
    silderValue = null;
    silderDivisions = null;
    silderMin = null;
    silderMax = null;
    type = SettingsTileType.radioTile;
  }

  final String title;
  late final String? details;
  late final String? subtitle;
  late final VoidCallback? onPress;
  late final SettingsTileType type;
  late final Widget? suffix;
  late final void Function(bool)? onBoolChange;
  late final void Function(double)? onSilderChange;
  late final void Function(String)? onRadioChange;
  late final Map<String, String>? radioOptions;
  late final bool? switchValue;
  late final double? silderValue;
  late final int? silderDivisions;
  late final double? silderMin;
  late final double? silderMax;
  late final String? radioValue;

  @override
  Widget build(BuildContext context) {
    switch (type) {
      case SettingsTileType.simpleTile:
        return _buildSimpleTile(context);
      case SettingsTileType.switchTile:
        return _buildSwitchTile(context);
      case SettingsTileType.sliderTile:
        return _buildSliderTile(context);
      case SettingsTileType.navigationTile:
        return _buildSimpleTile(context);
      case SettingsTileType.radioTile:
        return _buildRadioTile(context);
    }
  }

  Widget _buildSimpleTile(BuildContext context) {
    final subtitleStyle = context.theme.itemStyle.contentStyle.subtitleTextStyle
        .resolve({});
    return FTile(
      style:
          tileStyle(
            colors: context.theme.colors,
            typography: context.theme.typography,
            style: context.theme.style,
          ).call,
      title: ConstrainedBox(
        constraints: BoxConstraints(minHeight: 40),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(title, maxLines: 2),
            subtitle == null
                ? SizedBox()
                : Text(subtitle!, style: subtitleStyle),
          ],
        ),
      ),
      details: details == null ? null : Text(details!),
      suffix: suffix,
      onPress: onPress,
    );
  }

  Widget _buildSwitchTile(BuildContext context) {
    final subtitleStyle = context.theme.itemStyle.contentStyle.subtitleTextStyle
        .resolve({});
    return FTile(
      style:
          tileStyle(
            colors: context.theme.colors,
            typography: context.theme.typography,
            style: context.theme.style,
          ).call,
      title: ConstrainedBox(
        constraints: BoxConstraints(minHeight: 40),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(title),
            subtitle == null
                ? SizedBox()
                : Text(subtitle!, style: subtitleStyle),
          ],
        ),
      ),
      subtitle: subtitle == null ? null : Text(subtitle!),
      details: details == null ? null : Text(details!),
      onPress: onPress,
      suffix: SizedBox(
        height: 40,
        child: Switch(
          value: switchValue!,
          onChanged: (value) => onBoolChange!(value),
        ),
      ),
    );
  }

  Widget _buildSliderTile(BuildContext context) {
    final subtitleStyle = context.theme.itemStyle.contentStyle.subtitleTextStyle
        .resolve({});
    return FTile(
      style:
          tileStyle(
            colors: context.theme.colors,
            typography: context.theme.typography,
            style: context.theme.style,
          ).call,
      title: ConstrainedBox(
        constraints: BoxConstraints(minHeight: 40),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [Text(title), Text(details ?? '')],
            ),
            Slider(
              padding: EdgeInsets.only(top: 12, bottom: 4),
              value: silderValue!,
              min: silderMin!,
              max: silderMax!,
              divisions: silderDivisions,
              onChanged: (value) => onSilderChange!(value),
            ),
            subtitle == null || subtitle == ''
                ? SizedBox()
                : Text(subtitle!, style: subtitleStyle),
          ],
        ),
      ),
      suffix: suffix,
      onPress: onPress,
    );
  }

  Widget _buildRadioTile(BuildContext context) {
    return FSelectMenuTile.fromMap(
      radioOptions!,
      style:
          selectMenuTileStyle(
            colors: context.theme.colors,
            typography: context.theme.typography,
            style: context.theme.style,
          ).call,
      title: Text(title),
      initialValue: radioValue,
      subtitle: subtitle == null ? null : Text(subtitle!),
      details: Text(
        radioOptions!.entries.firstWhere((e) => e.value == radioValue).key,
      ),
      suffix: suffix,
      onChange: (value) => onRadioChange!(value.first),
    );
  }
}
